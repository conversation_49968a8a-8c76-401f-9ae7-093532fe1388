"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Heart, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import ReactionPicker, { reactionOptions } from "./ReactionPicker";

interface ReactionData {
  type: string;
  count: number;
  userReacted: boolean;
}

interface ReactionButtonProps {
  checkinId: string;
  className?: string;
}

export default function ReactionButton({ checkinId, className = "" }: ReactionButtonProps) {
  const [reactions, setReactions] = useState<ReactionData[]>([]);
  const [userReaction, setUserReaction] = useState<string | null>(null);
  const [isPickerOpen, setIsPickerOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Fetch reactions data
  const fetchReactions = async () => {
    try {
      const response = await fetch(`/api/reactions?checkinId=${checkinId}`);
      if (response.ok) {
        const data = await response.json();
        setReactions(data.reactions || []);
        setUserReaction(data.userReaction);
      }
    } catch (error) {
      console.error("Failed to fetch reactions:", error);
    }
  };

  useEffect(() => {
    fetchReactions();
  }, [checkinId]);

  const handleReactionSelect = async (reactionType: string) => {
    if (isLoading) return;

    setIsLoading(true);

    try {
      const response = await fetch("/api/reactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          checkinId,
          reactionType,
        }),
      });

      if (response.ok) {
        // Optimistically update the UI with animation
        const reactionOption = reactionOptions.find(r => r.type === reactionType);

        // Update reactions state
        setReactions(prev => {
          const updated = [...prev];
          const existingIndex = updated.findIndex(r => r.type === reactionType);
          const userPrevReactionIndex = updated.findIndex(r => r.type === userReaction);

          // Remove count from previous reaction if user had one
          if (userReaction && userPrevReactionIndex !== -1) {
            updated[userPrevReactionIndex].count = Math.max(0, updated[userPrevReactionIndex].count - 1);
            updated[userPrevReactionIndex].userReacted = false;
            if (updated[userPrevReactionIndex].count === 0) {
              updated.splice(userPrevReactionIndex, 1);
            }
          }

          // Add or update new reaction
          if (existingIndex !== -1) {
            updated[existingIndex].count += 1;
            updated[existingIndex].userReacted = true;
          } else {
            updated.push({
              type: reactionType,
              count: 1,
              userReacted: true
            });
          }

          return updated;
        });

        setUserReaction(reactionType);

        // Show success toast with emoji
        toast.success(
          <div className="flex items-center gap-2">
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: [0, 1.2, 1] }}
              transition={{ duration: 0.3 }}
            >
              {reactionOption?.emoji}
            </motion.span>
            <span>Reacted with {reactionOption?.label}!</span>
          </div>
        );
      } else {
        toast.error("Failed to add reaction");
      }
    } catch (error) {
      console.error("Failed to add reaction:", error);
      toast.error("Failed to add reaction");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveReaction = async () => {
    if (isLoading || !userReaction) return;
    
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/reactions?checkinId=${checkinId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        // Optimistically update the UI
        setReactions(prev => {
          const updated = [...prev];
          const reactionIndex = updated.findIndex(r => r.type === userReaction);
          
          if (reactionIndex !== -1) {
            updated[reactionIndex].count = Math.max(0, updated[reactionIndex].count - 1);
            updated[reactionIndex].userReacted = false;
            if (updated[reactionIndex].count === 0) {
              updated.splice(reactionIndex, 1);
            }
          }
          
          return updated;
        });
        
        setUserReaction(null);
        toast.success("Reaction removed");
      } else {
        toast.error("Failed to remove reaction");
      }
    } catch (error) {
      console.error("Failed to remove reaction:", error);
      toast.error("Failed to remove reaction");
    } finally {
      setIsLoading(false);
    }
  };

  const totalReactions = reactions.reduce((sum, r) => sum + r.count, 0);
  const currentReactionOption = userReaction 
    ? reactionOptions.find(r => r.type === userReaction)
    : null;

  return (
    <div className={`relative ${className}`}>
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.1 }}
      >
        <Button
          ref={buttonRef}
          variant="ghost"
          size="sm"
          onClick={() => {
            if (userReaction) {
              handleRemoveReaction();
            } else {
              setIsPickerOpen(!isPickerOpen);
            }
          }}
          disabled={isLoading}
          className={`
            h-8 px-2 rounded-full transition-all duration-200
            ${userReaction
              ? 'bg-pink-100 dark:bg-pink-900/30 hover:bg-pink-200 dark:hover:bg-pink-900/50 text-pink-600 dark:text-pink-400'
              : 'hover:bg-pink-50 dark:hover:bg-pink-900/20 hover:text-pink-600 dark:hover:text-pink-400'
            }
          `}
        >
          <motion.div
            className="flex items-center gap-1"
            animate={{
              scale: isLoading ? 0.95 : 1,
              opacity: isLoading ? 0.7 : 1
            }}
            transition={{ duration: 0.1 }}
          >
            {currentReactionOption ? (
              <motion.span
                key={currentReactionOption.type}
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 25
                }}
                className="text-sm"
              >
                {currentReactionOption.emoji}
              </motion.span>
            ) : (
              <motion.div
                animate={{
                  scale: isPickerOpen ? 1.1 : 1,
                  rotate: isPickerOpen ? 15 : 0
                }}
                transition={{ duration: 0.2 }}
              >
                <Heart className="h-4 w-4" />
              </motion.div>
            )}

            <AnimatePresence mode="wait">
              <motion.span
                key={totalReactions}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                transition={{ duration: 0.15 }}
                className="text-xs"
              >
                {totalReactions > 0 ? totalReactions : "React"}
              </motion.span>
            </AnimatePresence>
          </motion.div>
        </Button>
      </motion.div>

      {/* Add reaction button when user has no reaction */}
      {!userReaction && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsPickerOpen(!isPickerOpen)}
          className="h-8 w-8 p-0 rounded-full ml-1 hover:bg-slate-100 dark:hover:bg-slate-700"
        >
          <Plus className="h-3 w-3" />
        </Button>
      )}

      <ReactionPicker
        isOpen={isPickerOpen}
        onSelect={handleReactionSelect}
        onClose={() => setIsPickerOpen(false)}
        currentReaction={userReaction}
      />

      {/* Reaction summary */}
      <AnimatePresence>
        {reactions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 5, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 5, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className="absolute -bottom-6 left-0 flex items-center gap-1 text-xs text-slate-600 dark:text-slate-400"
          >
            {reactions.slice(0, 3).map((reaction, index) => {
              const option = reactionOptions.find(r => r.type === reaction.type);
              return (
                <motion.div
                  key={reaction.type}
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.5 }}
                  transition={{
                    delay: index * 0.05,
                    type: "spring",
                    stiffness: 300,
                    damping: 25
                  }}
                  whileHover={{ scale: 1.1 }}
                  className="flex items-center gap-0.5 cursor-default"
                >
                  <motion.span
                    animate={{
                      scale: reaction.userReacted ? [1, 1.2, 1] : 1
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    {option?.emoji}
                  </motion.span>
                  <motion.span
                    key={reaction.count}
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.2 }}
                  >
                    {reaction.count}
                  </motion.span>
                </motion.div>
              );
            })}
            {reactions.length > 3 && (
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-slate-500"
              >
                +{reactions.length - 3}
              </motion.span>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
